cmake_minimum_required(VERSION 3.16)
project(libkskyb)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS ON)  # Enable GNU extensions for gnu++11
set(CMAKE_C_STANDARD 99)

# 컴파일 플래그 설정 (makefile의 -fPIC 옵션 반영)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -w")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -w")

# Include 디렉토리 설정
include_directories(inc)

# 출력 디렉토리 설정
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)

# lib과 obj 디렉토리 생성
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/obj)

# qdata 라이브러리
add_library(libqdata STATIC src/qdata.cpp)
set_target_properties(libqdata PROPERTIES OUTPUT_NAME qdata)

# ksqueue 라이브러리
add_library(libksqueue STATIC src/ksqueue.cpp)
set_target_properties(libksqueue PROPERTIES OUTPUT_NAME ksqueue)

# kqueue 라이브러리 (C 소스)
add_library(libkqueue STATIC src/kqueue.c)
set_target_properties(libkqueue PROPERTIES OUTPUT_NAME kqueue)

# kscommon 라이브러리
add_library(libkscommon STATIC src/kscommon.cpp)
set_target_properties(libkscommon PROPERTIES OUTPUT_NAME kscommon)

# ksconfig 라이브러리
add_library(libksconfig STATIC src/ksconfig.cpp)
set_target_properties(libksconfig PROPERTIES OUTPUT_NAME ksconfig)

# ksbase64 라이브러리
add_library(libksbase64 STATIC src/ksbase64.cpp)
set_target_properties(libksbase64 PROPERTIES OUTPUT_NAME ksbase64)

# ksqueuetable 라이브러리 (ksqueue에 의존)
add_library(libksqueuetable STATIC src/ksqueuetable.cpp)
set_target_properties(libksqueuetable PROPERTIES OUTPUT_NAME ksqueuetable)
add_dependencies(libksqueuetable libksqueue)

# kssocket 라이브러리
add_library(libkssocket STATIC src/kssocket.cpp)
set_target_properties(libkssocket PROPERTIES OUTPUT_NAME kssocket)

# ksthread 라이브러리
add_library(libksthread STATIC src/ksthread.cpp)
set_target_properties(libksthread PROPERTIES OUTPUT_NAME ksthread)

# ksseedbyte 라이브러리 (여러 소스 파일)
add_library(libksseedbyte STATIC 
    src/ksseedbyte.cpp
    src/Seedx-1.c
)
set_target_properties(libksseedbyte PROPERTIES OUTPUT_NAME ksseedbyte)

# 모든 라이브러리에 대한 타겟 그룹
add_custom_target(all_libkskyb ALL
    DEPENDS libqdata libksqueue libkqueue libkscommon libksconfig libksbase64 libksqueuetable libkssocket libksthread libksseedbyte
)

# 라이브러리를 $HOME/library에 설치하는 타겟
add_custom_target(install_libkskyb
    COMMAND ${CMAKE_COMMAND} -E make_directory $ENV{HOME}/library
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libqdata.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksqueue.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libkqueue.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libkscommon.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksconfig.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksbase64.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksqueuetable.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libkssocket.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksthread.a $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/lib/libksseedbyte.a $ENV{HOME}/library/
    # 헤더 파일들도 복사
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/qdata.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksqueue.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/kqueue.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/kscommon.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksconfig.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksbase64.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksqueuetable.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/kssocket.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksthread.h $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/inc/ksseedbyte.h $ENV{HOME}/library/
    DEPENDS all_libkskyb
    COMMENT "Installing libkskyb libraries and headers to $HOME/library"
)

# clean 타겟
add_custom_target(clean_libkskyb
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_SOURCE_DIR}/obj
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_SOURCE_DIR}/lib
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_CURRENT_SOURCE_DIR}/obj
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_CURRENT_SOURCE_DIR}/lib
    COMMENT "Cleaning libkskyb build artifacts"
)
